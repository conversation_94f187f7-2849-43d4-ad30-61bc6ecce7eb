/* Custom animations for the application */

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-fade-in-delayed {
  animation: fade-in 0.8s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
}

.animate-slide-down {
  animation: slide-down 0.4s ease-out forwards;
}
