<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class EmailVerificationPromptController extends Controller
{
    /**
     * Show the email verification prompt page.
     */
    public function __invoke(Request $request): Response|RedirectResponse
    {
        return $request->user()->hasVerifiedEmail()
                    ? ($request->user()->role === 'admin'
                        ? redirect()->intended(route('admin.dashboard', absolute: false))
                        : redirect()->intended(route('penduduk.dashboard', absolute: false)))
                    : Inertia::render('auth/verify-email', ['status' => $request->session()->get('status')]);
    }
}
