<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Seed the admin user.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin'),
            'role' => 'admin',
        ]);
    }
}
